import React, { useRef, useState } from 'react';
import './upload-model-modal.css';
import { UploadCloud, X } from 'lucide-react';
import { PrimaryButton } from '../primary-button/primary-button';
import { SecondaryButton } from '../secondary-button/secondary-button';

interface UploadModelModalProps {
  visible: boolean;
  onClose: () => void;
  onUpload: (file: File) => void;
}

export const UploadModelModal: React.FC<UploadModelModalProps> = ({ visible, onClose, onUpload }) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  if (!visible) return null;

  const handleFile = (file?: File) => {
    if (!file) return;
    const ext = file.name.split('.').pop()?.toLowerCase() || '';
    if (!['glb', 'gltf'].includes(ext)) {
      alert('只支持 GLB / GLTF 格式文件');
      return;
    }
    setSelectedFile(file);
  };

  const handleDrop: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
    const file = e.dataTransfer.files?.[0];
    handleFile(file);
  };

  const handleDragOver: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!dragOver) setDragOver(true);
  };

  const handleDragLeave: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-dialog" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <span>上传模型</span>
          <X size={18} className="close-icon" onClick={onClose} />
        </div>

        <div
          className={`upload-area ${dragOver ? 'drag-over' : ''}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => inputRef.current?.click()}
        >
          <UploadCloud size={48} />
          <p>点击选择模型文件，或拖拽到此处上传</p>
          <p style={{ color: 'gray' }}>支持格式：glb, gltf</p>
          {selectedFile && <p className="file-name">已选择: {selectedFile.name}</p>}
        </div>

        <input
          ref={inputRef}
          type="file"
          accept=".glb,.gltf"
          style={{ display: 'none' }}
          onChange={(e) => handleFile(e.target.files?.[0])}
        />

        <div className="modal-actions">
          <SecondaryButton onClick={onClose} showIcon={false}>取消</SecondaryButton>
          <PrimaryButton
            disabled={!selectedFile}
            onClick={() => {
              if (selectedFile) {
                onUpload(selectedFile);
              }
            }}
            showIcon={false}
          >
            确定
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};
