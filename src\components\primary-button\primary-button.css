.primary-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: var(--color-brand);
  color: var(--color-content-invert);
  border: none;
  border-radius: var(--radius-m);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: 8px 16px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.primary-button:hover:not(:disabled) {
  background: var(--color-brand-hover);
}

.primary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.primary-button__icon {
  width: 16px;
  height: 16px;
}

/* Size variants */
.primary-button--small {
  padding: 6px 12px;
  font-size: 12px;
}
.primary-button--large {
  padding: 10px 20px;
  font-size: 16px;
}

/* Full width */
.primary-button--full-width {
  width: 100%;
  justify-content: center;
}
