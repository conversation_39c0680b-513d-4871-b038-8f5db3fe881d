.icon-button {
  align-items: center;
  background-color: var(--color-support);
  border: none;
  border-radius: var(--radius-m);
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  padding: 0;
  position: relative;
  transition: background-color 0.2s ease, opacity 0.2s ease;
  height: 32px;
  width: 32px;
}

.icon-button:hover:not(:disabled) {
  background-color: var(--color-bg-hover);
}

.icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon-button__icon {
  height: 16px;
  width: 16px;
  color: var(--color-content-accent);
}

/* 悬停状态 */
.icon-button:hover:not(:disabled) {
  background-color: var(--color-support-hover);
}

/* 禁用状态 */
.icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 激活状态 */
.icon-button:active:not(:disabled) {
  background-color: var(--color-support-hover);
  transform: translateY(1px);
}