:root {
    /* === 字体 === */
    --font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-weight-regular: 400;
    --font-weight-medium: 500;

    /* === 圆角 === */
    --radius-s: 4px;
    --radius-m: 8px;
    --radius-l: 16px;

    /* === 阴影 === */
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);

    /* === 状态色 === */
    --color-success: #008e5e;
    --color-error: #EF4444;
    --color-content-invert: #FFFFFF;
}

.theme-dark {
    /* === 品牌色 === */
    --color-brand: #2269EC;
    --color-brand-hover: #0B51D3;

    /* === 辅助色 === */
    --color-support: rgba(255, 255, 255, 0.12);

    /* === 边框 === */
    --color-border: rgba(255, 255, 255, 0.12);

    /* === 内容颜色 === */
    --color-content-accent: rgba(255, 255, 255, 0.9);
    --color-content-regular: rgba(255, 255, 255, 0.7);
    --color-content-secondary: rgba(255, 255, 255, 0.3);
    --color-content-invert: #FFFFFF;

    /* === 背景 === */
    --color-bg-page: #1A1A1A;
    --color-bg-dialog: #1F1F1F;
    --color-bg-primary: #272727;
    --color-bg-overlay: rgba(255, 255, 255, 0.05);
    --color-bg-input: rgba(255, 255, 255, 0.07);
    --color-bg-hover: rgba(255, 255, 255, 0.05);
}
