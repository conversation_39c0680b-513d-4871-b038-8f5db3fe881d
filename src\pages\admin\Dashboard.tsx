import { useState, useEffect } from 'react';
import { TabItem } from '../../components/tab-item/tab-item';
import { TabGroup } from '../../components/tab-group/tab-group';
import { IconButton } from '../../components/icon-button/icon-button';
import { FileText, Grid, Palette, LogOut, Home } from 'lucide-react';
import './Dashboard.css';
import ModelManagement from './ModelManagement';
import MaterialManagement from './MaterialManagement';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // 检查用户是否已登录
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      window.location.href = '/admin';
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    window.location.href = '/admin';
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <Overview />;
      case 'models':
        return <ModelManagement />;
      case 'materials':
        return <MaterialManagement />;
      default:
        return <Overview />;
    }
  };

  return (
    <div className="dashboard-container theme-dark">
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <div className="logo-container" onClick={() => window.location.href = '/'}>
            <img className="logo" src="/images/Logo.png" alt="RINKO" />
            <div className="logo-tooltip">返回前台页面</div>
          </div>
          <h2>后台管理</h2>
        </div>
        
        <div className="sidebar-menu">
          <TabGroup direction="vertical" onChange={(index) => {
            const tabs = ['overview', 'models', 'materials'];
            setActiveTab(tabs[index]);
          }}>
            <TabItem
              label="总览"
              icon={Home}
              width={180}
            />
            <TabItem
              label="模型管理"
              icon={FileText}
              width={180}
            />
            <TabItem
              label="材质管理"
              icon={Palette}
              width={180}
            />
          </TabGroup>
        </div>
        
        <div className="sidebar-footer">
          <div className="logout-button" onClick={handleLogout}>
            <LogOut size={16} />
            <span>退出登录</span>
          </div>
        </div>
      </div>
      
      <div className="dashboard-content">
        <div className="content-header">
          <h1>{activeTab === 'overview' ? '总览' : activeTab === 'models' ? '模型管理' : '材质管理'}</h1>
          <div className="header-actions">
            <IconButton 
              icon={LogOut} 
              onClick={handleLogout} 
              size="medium" 
            />
          </div>
        </div>
        
        <div className="content-body">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

// 简单的总览组件
const Overview = () => {
  return (
    <div className="overview-container">
      <div className="stats-row">
        <div className="stat-card">
          <div className="stat-icon">
            <FileText size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">12</div>
            <div className="stat-label">模型总数</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">
            <Palette size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">48</div>
            <div className="stat-label">材质总数</div>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">
            <Grid size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">156</div>
            <div className="stat-label">用户访问</div>
          </div>
        </div>
      </div>
      
      <div className="quick-actions">
        <h3>快捷操作</h3>
        <div className="quick-action-links">
          <div className="quick-action-item" onClick={() => window.location.href = '/'}>
            <Home size={20} />
            <span>前往前台页面</span>
          </div>
          <div className="quick-action-item" onClick={() => window.location.hash = '#models'}>
            <FileText size={20} />
            <span>管理模型</span>
          </div>
          <div className="quick-action-item" onClick={() => window.location.hash = '#materials'}>
            <Palette size={20} />
            <span>管理材质</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
