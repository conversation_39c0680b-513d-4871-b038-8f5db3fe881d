import React from "react";
import type { LucideIcon } from "lucide-react";
import { Plus } from "lucide-react";
import "./primary-button.css";

interface PrimaryButtonProps {
  showIcon?: boolean;
  children: React.ReactNode;
  icon?: LucideIcon;
  className?: string;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: "button" | "submit" | "reset";
  fullWidth?: boolean;
}

export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  children,
  icon: Icon = Plus,
  className = "",
  disabled = false,
  onClick,
  type = "button",
  fullWidth = false,
  showIcon = true,
}: PrimaryButtonProps) => {
  return (
    <button
      className={`primary-button ${fullWidth ? "primary-button--full-width" : ""} ${className}`}
      disabled={disabled}
      onClick={onClick}
      type={type}
    >
      {showIcon && Icon && <Icon className="primary-button__icon" />}
      <span className="primary-button__text">{children}</span>
    </button>
  );
};
