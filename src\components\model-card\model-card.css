.model-card {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-family: var(--font-family);
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
}

.model-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.model-card:active {
  transform: translateY(-2px);
}

.model-card__image-container {
  position: relative;
  width: 100%;
  flex: 1;
  background: var(--color-bg-overlay);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.model-card:hover .model-card__image {
  transform: scale(1.05);
}

.model-card__placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-overlay);
  color: var(--color-content-secondary);
}

.model-card__placeholder.hidden {
  display: none;
}

.model-card__placeholder-icon {
  opacity: 0.5;
}

.model-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(34, 105, 236, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.model-card:hover .model-card__overlay {
  opacity: 1;
}

.model-card__overlay-text {
  color: var(--color-content-invert);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family);
  line-height: 1;
}

.model-card__content {
  padding: 12px 16px 16px 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: var(--color-bg-primary);
}

.model-card__title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family);
  color: var(--color-content-accent);
  margin: 0;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.model-card__meta {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--font-size-sm);
  font-family: var(--font-family);
  color: var(--color-content-regular);
  line-height: 1;
}

.model-card__file-type {
  background: var(--color-bg-overlay);
  padding: 2px 8px;
  border-radius: var(--radius-s);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--color-content-regular);
  line-height: 1;
}

.model-card__size {
  color: var(--color-content-regular);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

@media (max-width: 768px) {
  .model-card__content {
    padding: 10px 12px 12px 12px;
  }

  .model-card__title,
  .model-card__meta,
  .model-card__file-type,
  .model-card__size {
    font-size: 12px;
  }
}

.model-card--loading {
  pointer-events: none;
  opacity: 0.6;
}

.model-card--loading .model-card__image-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 1.5s infinite;
}


