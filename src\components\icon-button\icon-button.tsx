import React from "react";
import type { LucideIcon } from "lucide-react";
import { Plus } from "lucide-react";
import "./icon-button.css";

interface IconButtonProps {
  icon?: LucideIcon;
  className?: string;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  children?: React.ReactNode;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon: Icon = Plus,
  className = "",
  disabled = false,
  onClick,
  type = "button",
  children
}: IconButtonProps) => {
  return (
    <button
      className={`icon-button ${className}`}
      disabled={disabled}
      onClick={onClick}
      type={type}
    >
      {Icon && <Icon className="icon-button__icon" />}
      {children}
    </button>
  );
};
