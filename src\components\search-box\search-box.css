.search-box {
  padding: 6px 8px;
  background: var(--color-bg-input);
  border-radius: var(--radius-m);
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  height: 32px;
  box-sizing: border-box;
  cursor: text;
}

.search-box__icon-container {
  width: 16px;
  height: 16px;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.search-box__icon {
  color: var(--color-content-mute);
  stroke-width: 1px;
}

.search-box__input {
  flex-grow: 1;
  border: none;
  background: transparent;
  outline: none;
  padding: 0;
  color: var(--color-content-accent);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.search-box__input::placeholder {
  color: var(--color-content-mute);
}

/* 禁用状态 */
.search-box--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-box--disabled .search-box__input {
  cursor: not-allowed;
  pointer-events: none;
}

.search-box--disabled .search-box__icon-container {
  cursor: not-allowed;
}

/* 适配深色/浅色主题 */
.theme-light .search-box__icon {
  color: var(--color-content-mute);
}

.theme-light .search-box__input::placeholder {
  color: var(--color-content-mute);
}
