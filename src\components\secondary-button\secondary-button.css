.secondary-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: transparent;
  color: var(--color-content-accent);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-m);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: 8px 16px;
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
}

.secondary-button:hover:not(:disabled) {
  background: var(--color-bg-hover);
}

.secondary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.secondary-button__icon {
  width: 16px;
  height: 16px;
}

.secondary-button--full-width {
  width: 100%;
  justify-content: center;
}
.secondary-button--full-width {
  width: 100%;
  justify-content: center;
}
